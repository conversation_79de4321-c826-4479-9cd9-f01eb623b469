CREATE TABLE T_SZ_TAX_A_ENT (
    TAXPAYER_ID            VARCHAR2(20)   NOT NULL,  -- 纳税人识别号，唯一标识企业
    TAXPAYER_NAME          VARCHAR2(200)  NOT NULL,  -- 纳税人名称，企业名称
    EVALUATION_YEAR        NUMBER(4)      NOT NULL,  -- 评价年度
    CREATE_TIME            DATE           DEFAULT SYSDATE NOT NULL  -- 创建时间，默认当前时间  -- 创建时间，记录数据的创建时间
);

-- 添加字段备注
COMMENT ON COLUMN T_SZ_TAX_A_ENT.TAXPAYER_ID IS '纳税人识别号，唯一标识企业';
COMMENT ON COLUMN T_SZ_TAX_A_ENT.TAXPAYER_NAME IS '纳税人名称，企业名称';
COMMENT ON COLUMN T_SZ_TAX_A_ENT.EVALUATION_YEAR IS '评价年度，表示纳税人评价的年份';
COMMENT ON COLUMN T_SZ_TAX_A_ENT.CREATE_TIME IS '创建时间，记录数据的创建时间';


