

-- 1. 创建专精特新企业认定信息表（T_SZGXJ_SRDI_RECOG_INFO）
--一个企业可以有多条认定记录，保留所有历史。

CREATE TABLE T_SZGXJ_SRDI_RECOG_INFO (
    ID          NUMBER         NOT NULL,  -- 主键ID
    TYSHXYDM    VARCHAR2(50)   NOT NULL,  -- 统一社会信用代码
    QYMC        VARCHAR2(250),            -- 企业名称
    JLID        NUMBER,                   -- 记录ID
    QYLX        NUMBER,                   -- 企业类型
    ZSMC        VARCHAR2(200),            -- 证书名称
    KSSJ        DATE,                     -- 开始时间
    JSSJ        DATE,                     -- 结束时间
    BFBM        VARCHAR2(200),            -- 颁发部门
    ZSZT        VARCHAR2(50),             -- 证书状态
    QYZT        VARCHAR2(50),             -- 企业状态
    QYPC        NUMBER,                   -- 企业批次
    QYPCZFC     VARCHAR2(100),            -- 企业批次字符串
    PDSJ        DATE,                     -- 判定时间
    YXQ         NUMBER,                   -- 有效期
    ZSBH        VARCHAR2(100),            -- 证书编号
    PX          NUMBER,                   -- 排序
    QUERY_STATUS NUMBER(1)      DEFAULT 0 NOT NULL,  -- 查询状态：0=待查询, 1=已查询, 2=查询失败
    QUERY_TIME  DATE,                     -- 查询时间
    QUERY_MESSAGE VARCHAR2(500),            -- 查询消息
    IS_HISTORY_RECORD NUMBER(1)    DEFAULT 0 NOT NULL,  -- 是否历史记录: 0=当前, 1=历史
    CREATE_TIME DATE           DEFAULT SYSDATE NOT NULL, -- 记录创建时间
    UPDATE_TIME DATE           DEFAULT SYSDATE NOT NULL  -- 记录更新时间
);

CREATE SEQUENCE SEQ_SZGXJ_SRDI_RECOG_INFO_ID START WITH 1 INCREMENT BY 1 NOCACHE NOCYCLE;

CREATE OR REPLACE TRIGGER TRG_SZGXJ_SRDI_RECOG_INFO_BIU
BEFORE INSERT OR UPDATE ON T_SZGXJ_SRDI_RECOG_INFO
FOR EACH ROW
BEGIN
    IF INSERTING THEN
        IF :NEW.ID IS NULL THEN
            SELECT SEQ_SZGXJ_SRDI_RECOG_INFO_ID.NEXTVAL INTO :NEW.ID FROM DUAL;
        END IF;
        :NEW.CREATE_TIME := SYSDATE;
    END IF;
    :NEW.UPDATE_TIME := SYSDATE;
END;
/

ALTER TABLE T_SZGXJ_SRDI_RECOG_INFO ADD CONSTRAINT PK_SZGXJ_SRDI_RECOG_INFO PRIMARY KEY (ID);
CREATE INDEX IDX_SZGXJ_SRDI_RECOG_TYSHXYDM ON T_SZGXJ_SRDI_RECOG_INFO(TYSHXYDM);
CREATE INDEX IDX_SZGXJ_SRDI_RECOG_Q_STATUS ON T_SZGXJ_SRDI_RECOG_INFO(QUERY_STATUS);
CREATE INDEX IDX_SZGXJ_SRDI_RECOG_HISTORY ON T_SZGXJ_SRDI_RECOG_INFO(TYSHXYDM, IS_HISTORY_RECORD);

COMMENT ON TABLE T_SZGXJ_SRDI_RECOG_INFO IS '专精特新企业认定信息表 (源表, 一个企业可对应多条认定记录, 保留历史)';
COMMENT ON COLUMN T_SZGXJ_SRDI_RECOG_INFO.ID IS '主键ID';
COMMENT ON COLUMN T_SZGXJ_SRDI_RECOG_INFO.TYSHXYDM IS '统一社会信用代码';
COMMENT ON COLUMN T_SZGXJ_SRDI_RECOG_INFO.QYMC IS '企业名称';
COMMENT ON COLUMN T_SZGXJ_SRDI_RECOG_INFO.JLID IS '记录ID';
COMMENT ON COLUMN T_SZGXJ_SRDI_RECOG_INFO.QYLX IS '企业类型';
COMMENT ON COLUMN T_SZGXJ_SRDI_RECOG_INFO.ZSMC IS '证书名称';
COMMENT ON COLUMN T_SZGXJ_SRDI_RECOG_INFO.KSSJ IS '开始时间';
COMMENT ON COLUMN T_SZGXJ_SRDI_RECOG_INFO.JSSJ IS '结束时间';
COMMENT ON COLUMN T_SZGXJ_SRDI_RECOG_INFO.BFBM IS '颁发部门';
COMMENT ON COLUMN T_SZGXJ_SRDI_RECOG_INFO.ZSZT IS '证书状态';
COMMENT ON COLUMN T_SZGXJ_SRDI_RECOG_INFO.QYZT IS '企业状态';
COMMENT ON COLUMN T_SZGXJ_SRDI_RECOG_INFO.QYPC IS '企业批次';
COMMENT ON COLUMN T_SZGXJ_SRDI_RECOG_INFO.QYPCZFC IS '企业批次字符串';
COMMENT ON COLUMN T_SZGXJ_SRDI_RECOG_INFO.PDSJ IS '判定时间';
COMMENT ON COLUMN T_SZGXJ_SRDI_RECOG_INFO.YXQ IS '有效期';
COMMENT ON COLUMN T_SZGXJ_SRDI_RECOG_INFO.ZSBH IS '证书编号';
COMMENT ON COLUMN T_SZGXJ_SRDI_RECOG_INFO.PX IS '排序';
COMMENT ON COLUMN T_SZGXJ_SRDI_RECOG_INFO.QUERY_STATUS IS '查询状态：0=待查询, 1=已查询, 2=查询失败';
COMMENT ON COLUMN T_SZGXJ_SRDI_RECOG_INFO.QUERY_TIME IS '查询时间';
COMMENT ON COLUMN T_SZGXJ_SRDI_RECOG_INFO.QUERY_MESSAGE IS '查询消息';
COMMENT ON COLUMN T_SZGXJ_SRDI_RECOG_INFO.IS_HISTORY_RECORD IS '是否历史记录: 0=当前, 1=历史';
COMMENT ON COLUMN T_SZGXJ_SRDI_RECOG_INFO.CREATE_TIME IS '记录创建时间';
COMMENT ON COLUMN T_SZGXJ_SRDI_RECOG_INFO.UPDATE_TIME IS '记录更新时间';

-- ================================================================================
-- 数据更新方案  Oracle 11g  
-- 全局临时表 (GTT) +  存储过程 的模式，简化Python端逻辑。
-- ================================================================================

-- -- 步骤1: 创建一个全局临时表，用于从Python批量接收新数据。
-- CREATE GLOBAL TEMPORARY TABLE T_SZGXJ_SRDI_RECOG_TEMP (
--     -- 字段与主表业务字段对应即可，无需审计字段和主键
--     TYSHXYDM    VARCHAR2(50)   NOT NULL,
--     QYMC        VARCHAR2(250),
--     JLID        NUMBER,
--     QYLX        NUMBER,
--     ZSMC        VARCHAR2(200),
--     KSSJ        DATE,
--     JSSJ        DATE,
--     BFBM        VARCHAR2(200),
--     ZSZT        VARCHAR2(50),
--     QYZT        VARCHAR2(50),
--     QYPC        NUMBER,
--     QYPCZFC     VARCHAR2(100),
--     PDSJ        DATE,
--     YXQ         NUMBER,
--     ZSBH        VARCHAR2(100),
--     PX          NUMBER
-- ) ON COMMIT DELETE ROWS;

-- -- 步骤2: 创建存储过程，封装“先删后插”的同步逻辑。
-- CREATE OR REPLACE PROCEDURE P_SYNC_SZGXJ_SRDI_RECOG_INFO (
--     p_tyshxydm IN VARCHAR2
-- )
-- AS
-- BEGIN
--     -- 先删除该企业在主表中的所有旧记录
--     DELETE FROM T_SZGXJ_SRDI_RECOG_INFO WHERE TYSHXYDM = p_tyshxydm;

--     -- 然后，从临时表中将所有新记录一次性插入主表
--     INSERT INTO T_SZGXJ_SRDI_RECOG_INFO (
--         TYSHXYDM, QYMC, JLID, QYLX, ZSMC, KSSJ, JSSJ, BFBM, ZSZT, QYZT, 
--         QYPC, QYPCZFC, PDSJ, YXQ, ZSBH, PX, 
--         QUERY_STATUS, QUERY_TIME, QUERY_MESSAGE, IS_HISTORY_RECORD
--     )
--     SELECT
--         TYSHXYDM, QYMC, JLID, QYLX, ZSMC, KSSJ, JSSJ, BFBM, ZSZT, QYZT,
--         QYPC, QYPCZFC, PDSJ, YXQ, ZSBH, PX,
--         1, SYSDATE, '同步成功', 0 -- 状态和消息设为成功, IS_HISTORY_RECORD=0
--     FROM T_SZGXJ_SRDI_RECOG_TEMP
--     WHERE TYSHXYDM = p_tyshxydm;

--     -- 存储过程执行结束，自动提交事务
--     COMMIT;
-- EXCEPTION
--     -- 如果过程中出现任何错误，则回滚所有操作
--     WHEN OTHERS THEN
--         ROLLBACK;
--         RAISE; -- 将错误抛出给调用方
-- END P_SYNC_SZGXJ_SRDI_RECOG_INFO;
-- /

-- Python端调用逻辑说明:
--
-- 1. 调用API获取某企业的多条专精特新数据
--    api_results = call_your_api(tyshxydm) # -> 得到一个列表，如 [{'qymc':'公司A', ...}, ...]
--
-- 2. 如果有数据，使用 "executemany" 批量插入到临时表 T_SZGXJ_SRDI_RECOG_TEMP
--    if api_results:
--        cursor.executemany("INSERT INTO T_SZGXJ_SRDI_RECOG_TEMP (...) VALUES (...)", api_results)
--
-- 3. 调用本存储过程，完成最终的同步
--    cursor.callproc("P_SYNC_SZGXJ_SRDI_RECOG_INFO", [tyshxydm])
--
-- 4. 数据库会自动处理事务和临时表的清理工作。
--

-- ================================================================================
-- 2. 创建高新技术企业认定信息表（T_GXB_HNTE_RECOG_INFO）
-- 特点: 保留历史版本, 不再是一对一关系。
-- ================================================================================


CREATE TABLE T_GXB_HNTE_RECOG_INFO (
    ID          NUMBER         NOT NULL,  -- 主键ID
    TYSHXYDM    VARCHAR2(50)   NOT NULL,  -- 统一社会信用代码
    QYMC        VARCHAR2(250),            -- 企业名称
    RDJGMC      VARCHAR2(200),            -- 认定机构名称
    YXQKSRQ     DATE,                     -- 有效期开始日期
    YXQJSRQ     DATE,                     -- 有效期结束日期
    ZSBH        VARCHAR2(100),            -- 证书编号
    QUERY_STATUS NUMBER(1)      DEFAULT 0 NOT NULL,  -- 查询状态：0=待查询, 1=已查询, 2=查询失败
    QUERY_TIME  DATE,                     -- 查询时间
    QUERY_MESSAGE VARCHAR2(500),            -- 查询消息
    IS_HISTORY_RECORD NUMBER(1)    DEFAULT 0 NOT NULL,  -- 是否历史记录: 0=当前, 1=历史
    CREATE_TIME DATE           DEFAULT SYSDATE NOT NULL, -- 记录创建时间
    UPDATE_TIME DATE           DEFAULT SYSDATE NOT NULL  -- 记录更新时间
);

CREATE SEQUENCE SEQ_GXB_HNTE_RECOG_INFO_ID START WITH 1 INCREMENT BY 1 NOCACHE NOCYCLE;

CREATE OR REPLACE TRIGGER TRG_GXB_HNTE_RECOG_INFO_BIU
BEFORE INSERT OR UPDATE ON T_GXB_HNTE_RECOG_INFO
FOR EACH ROW
BEGIN
    IF INSERTING THEN
        IF :NEW.ID IS NULL THEN
            SELECT SEQ_GXB_HNTE_RECOG_INFO_ID.NEXTVAL INTO :NEW.ID FROM DUAL;
        END IF;
        :NEW.CREATE_TIME := SYSDATE;
    END IF;
    :NEW.UPDATE_TIME := SYSDATE;
END;
/

ALTER TABLE T_GXB_HNTE_RECOG_INFO ADD CONSTRAINT PK_GXB_HNTE_RECOG_INFO PRIMARY KEY (ID);
CREATE INDEX IDX_GXB_HNTE_RECOG_TYSHXYDM ON T_GXB_HNTE_RECOG_INFO(TYSHXYDM);
CREATE INDEX IDX_GXB_HNTE_RECOG_Q_STATUS ON T_GXB_HNTE_RECOG_INFO(QUERY_STATUS);
CREATE INDEX IDX_GXB_HNTE_RECOG_HISTORY ON T_GXB_HNTE_RECOG_INFO(TYSHXYDM, IS_HISTORY_RECORD);

COMMENT ON TABLE T_GXB_HNTE_RECOG_INFO IS '工业和信息化部---高新技术企业认定信息表 (源表, 保留历史)';
COMMENT ON COLUMN T_GXB_HNTE_RECOG_INFO.ID IS '主键ID';
COMMENT ON COLUMN T_GXB_HNTE_RECOG_INFO.TYSHXYDM IS '统一社会信用代码';
COMMENT ON COLUMN T_GXB_HNTE_RECOG_INFO.QYMC IS '企业名称';
COMMENT ON COLUMN T_GXB_HNTE_RECOG_INFO.RDJGMC IS '认定机构名称';
COMMENT ON COLUMN T_GXB_HNTE_RECOG_INFO.YXQKSRQ IS '有效期开始日期';
COMMENT ON COLUMN T_GXB_HNTE_RECOG_INFO.YXQJSRQ IS '有效期结束日期';
COMMENT ON COLUMN T_GXB_HNTE_RECOG_INFO.ZSBH IS '证书编号';
COMMENT ON COLUMN T_GXB_HNTE_RECOG_INFO.QUERY_STATUS IS '查询状态：0=待查询, 1=已查询, 2=查询失败';
COMMENT ON COLUMN T_GXB_HNTE_RECOG_INFO.QUERY_TIME IS '查询时间';
COMMENT ON COLUMN T_GXB_HNTE_RECOG_INFO.QUERY_MESSAGE IS '查询消息';
COMMENT ON COLUMN T_GXB_HNTE_RECOG_INFO.IS_HISTORY_RECORD IS '是否历史记录: 0=当前, 1=历史';
COMMENT ON COLUMN T_GXB_HNTE_RECOG_INFO.CREATE_TIME IS '记录创建时间';
COMMENT ON COLUMN T_GXB_HNTE_RECOG_INFO.UPDATE_TIME IS '记录更新时间';

-- ================================================================================
-- 3. 创建深圳纳税等级信息表（T_SZTAX_TAX_RATING_INFO）
-- 特点: 保留历史版本, 不再是一对一关系。
-- =================================...
CREATE TABLE T_SZTAX_TAX_RATING_INFO (
    ID          NUMBER         NOT NULL,  -- 主键ID
    TYSHXYDM    VARCHAR2(50)   NOT NULL,  -- 统一社会信用代码
    QYMC        VARCHAR2(250),            -- 企业名称
    PJND        NUMBER(4),                -- 评价年度
    QUERY_STATUS NUMBER(1)      DEFAULT 0 NOT NULL,  -- 查询状态：0=待查询, 1=已查询, 2=查询失败
    QUERY_TIME  DATE,                     -- 查询时间
    QUERY_MESSAGE VARCHAR2(500),            -- 查询消息
    IS_HISTORY_RECORD NUMBER(1)    DEFAULT 0 NOT NULL,  -- 是否历史记录: 0=当前, 1=历史
    CREATE_TIME DATE           DEFAULT SYSDATE NOT NULL,  -- 记录创建时间
    UPDATE_TIME DATE           DEFAULT SYSDATE NOT NULL,   -- 记录更新时间
    TAX_RATING  VARCHAR2(10)             -- 纳税等级 (A, B等)
);

CREATE SEQUENCE SEQ_SZTAX_TAX_RATING_INFO_ID START WITH 1 INCREMENT BY 1 NOCACHE NOCYCLE;

CREATE OR REPLACE TRIGGER TRG_SZTAX_TAX_RATING_INFO_BIU
BEFORE INSERT OR UPDATE ON T_SZTAX_TAX_RATING_INFO
FOR EACH ROW
BEGIN
    IF INSERTING THEN
        IF :NEW.ID IS NULL THEN
            SELECT SEQ_SZTAX_TAX_RATING_INFO_ID.NEXTVAL INTO :NEW.ID FROM DUAL;
        END IF;
        :NEW.CREATE_TIME := SYSDATE;
    END IF;
    :NEW.UPDATE_TIME := SYSDATE;
END;
/

ALTER TABLE T_SZTAX_TAX_RATING_INFO ADD CONSTRAINT PK_SZTAX_TAX_RATING_INFO PRIMARY KEY (ID);
CREATE INDEX IDX_SZTAX_TAX_RATING_TYSHXYDM ON T_SZTAX_TAX_RATING_INFO(TYSHXYDM);
CREATE INDEX IDX_SZTAX_TAX_RATING_Q_STATUS ON T_SZTAX_TAX_RATING_INFO(QUERY_STATUS);
CREATE INDEX IDX_SZTAX_TAX_RATING_HISTORY ON T_SZTAX_TAX_RATING_INFO(TYSHXYDM, IS_HISTORY_RECORD);

COMMENT ON TABLE T_SZTAX_TAX_RATING_INFO IS '国家税务总局深圳市税务局-深圳纳税等级信息表 (源表, 保留历史)';
COMMENT ON COLUMN T_SZTAX_TAX_RATING_INFO.ID IS '主键ID';
COMMENT ON COLUMN T_SZTAX_TAX_RATING_INFO.TYSHXYDM IS '统一社会信用代码';
COMMENT ON COLUMN T_SZTAX_TAX_RATING_INFO.QYMC IS '企业名称';
COMMENT ON COLUMN T_SZTAX_TAX_RATING_INFO.PJND IS '评价年度';
COMMENT ON COLUMN T_SZTAX_TAX_RATING_INFO.QUERY_STATUS IS '查询状态：0=待查询, 1=已查询, 2=查询失败';
COMMENT ON COLUMN T_SZTAX_TAX_RATING_INFO.QUERY_TIME IS '查询时间';
COMMENT ON COLUMN T_SZTAX_TAX_RATING_INFO.QUERY_MESSAGE IS '查询消息';
COMMENT ON COLUMN T_SZTAX_TAX_RATING_INFO.IS_HISTORY_RECORD IS '是否历史记录: 0=当前, 1=历史';
COMMENT ON COLUMN T_SZTAX_TAX_RATING_INFO.CREATE_TIME IS '记录创建时间';
COMMENT ON COLUMN T_SZTAX_TAX_RATING_INFO.UPDATE_TIME IS '记录更新时间'; 
COMMENT ON COLUMN T_SZTAX_TAX_RATING_INFO.TAX_RATING IS '纳税等级 (A, B等)';


--   业务层面  找哲文确定抽表数据。   多行数据的要看取哪些