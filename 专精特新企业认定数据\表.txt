CREATE TABLE T_ZJTX_RECOG_DATA (
    ENTERPRISE_CODE         VARCHAR2(18)   NOT NULL,  -- 企业编码，统一社会信用代码
    ENTERPRISE_NAME         VARCHAR2(200),            -- 企业名称
    RECORD_ID               NUMBER,                    -- 记录ID
    ENTERPRISE_TYPE         NUMBER,                    -- 企业类型
    CERTIFICATE_NAME        VARCHAR2(200),            -- 证书名称
    START_DATE              DATE,                      -- 开始时间
    END_DATE                DATE,                      -- 结束时间
    ISSUING_DEPARTMENT     VARCHAR2(200),            -- 颁发部门
    CERTIFICATE_STATUS     VARCHAR2(50),             -- 证书状态
    STATUS                  VARCHAR2(50),             -- 企业状态
    ENTERPRISE_BATCH        NUMBER,                    -- 企业批次
    ENTERPRISE_BATCH_STRING VARCHAR2(100),            -- 企业批次字符串
    DETERMINATION_DATE      DATE,                      -- 判定时间
    VALIDITY_PERIOD         NUMBER,                    -- 有效期（年）
    CERTIFICATE_NO          VARCHAR2(100),            -- 证书编号
    SORT_ORDER              NUMBER,                    -- 排序
    QUERY_TIME              DATE                       -- 查询时间
);

-- 添加字段备注
COMMENT ON COLUMN T_ZJTX_RECOG_DATA.ENTERPRISE_CODE IS '企业编码，统一社会信用代码';
COMMENT ON COLUMN T_ZJTX_RECOG_DATA.ENTERPRISE_NAME IS '企业名称';
COMMENT ON COLUMN T_ZJTX_RECOG_DATA.RECORD_ID IS '记录ID';
COMMENT ON COLUMN T_ZJTX_RECOG_DATA.ENTERPRISE_TYPE IS '企业类型';
COMMENT ON COLUMN T_ZJTX_RECOG_DATA.CERTIFICATE_NAME IS '证书名称';
COMMENT ON COLUMN T_ZJTX_RECOG_DATA.START_DATE IS '开始时间';
COMMENT ON COLUMN T_ZJTX_RECOG_DATA.END_DATE IS '结束时间';
COMMENT ON COLUMN T_ZJTX_RECOG_DATA.ISSUING_DEPARTMENT IS '颁发部门';
COMMENT ON COLUMN T_ZJTX_RECOG_DATA.CERTIFICATE_STATUS IS '证书状态';
COMMENT ON COLUMN T_ZJTX_RECOG_DATA.STATUS IS '企业状态';
COMMENT ON COLUMN T_ZJTX_RECOG_DATA.ENTERPRISE_BATCH IS '企业批次';
COMMENT ON COLUMN T_ZJTX_RECOG_DATA.ENTERPRISE_BATCH_STRING IS '企业批次字符串';
COMMENT ON COLUMN T_ZJTX_RECOG_DATA.DETERMINATION_DATE IS '判定时间';
COMMENT ON COLUMN T_ZJTX_RECOG_DATA.VALIDITY_PERIOD IS '有效期（年）';
COMMENT ON COLUMN T_ZJTX_RECOG_DATA.CERTIFICATE_NO IS '证书编号';
COMMENT ON COLUMN T_ZJTX_RECOG_DATA.SORT_ORDER IS '排序';
COMMENT ON COLUMN T_ZJTX_RECOG_DATA.QUERY_TIME IS '查询时间';
