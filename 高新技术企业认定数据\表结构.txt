CREATE TABLE T_HIGH_TECH_ENT_RECOG (
    ENTERPRISE_CODE        VARCHAR2(18)  NOT NULL,  -- 企业编码，统一社会信用代码
    ENTERPRISE_NAME        VARCHAR2(200) NULL,  -- 企业名称
    CERTIFICATION_ORGANIZATION VARCHAR2(200) NULL,  -- 认定机构名称
    VALIDITY_START_DATE    DATE NULL,  -- 有效期开始日期
    VALIDITY_END_DATE      DATE NULL,  -- 有效期结束日期
    STATUS                 VARCHAR2(50)  NULL,  -- 状态（如：有效、无效）
    CERTIFICATE_NO         VARCHAR2(100) NULL,  -- 证书编号
    RECORD_ID              VARCHAR2(100) NULL,  -- 记录ID
    QUERY_TIME             DATE NULL   -- 查询时间
);

-- 添加字段备注
COMMENT ON COLUMN T_HIGH_TECH_ENT_RECOG.ENTERPRISE_CODE IS '企业编码，统一社会信用代码';
COMMENT ON COLUMN T_HIGH_TECH_ENT_RECOG.ENTERPRISE_NAME IS '企业名称';
COMMENT ON COLUMN T_HIGH_TECH_ENT_RECOG.CERTIFICATION_ORGANIZATION IS '认定机构名称';
COMMENT ON COLUMN T_HIGH_TECH_ENT_RECOG.VALIDITY_START_DATE IS '有效期开始日期';
COMMENT ON COLUMN T_HIGH_TECH_ENT_RECOG.VALIDITY_END_DATE IS '有效期结束日期';
COMMENT ON COLUMN T_HIGH_TECH_ENT_RECOG.STATUS IS '状态，标识企业是否有效';
COMMENT ON COLUMN T_HIGH_TECH_ENT_RECOG.CERTIFICATE_NO IS '证书编号';
COMMENT ON COLUMN T_HIGH_TECH_ENT_RECOG.RECORD_ID IS '记录ID，唯一标识记录';
COMMENT ON COLUMN T_HIGH_TECH_ENT_RECOG.QUERY_TIME IS '查询时间，标识查询数据的时间';
